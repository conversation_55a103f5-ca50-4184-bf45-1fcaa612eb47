#!/usr/bin/env python3
"""
Test MySQL database connection
"""

import mysql.connector
from mysql.connector import <PERSON>rror
from config import settings

def test_mysql_connection():
    """Test MySQL database connection"""
    print("Testing MySQL connection...")
    print(f"Host: {settings.db_host}")
    print(f"Port: {settings.db_port}")
    print(f"Database: {settings.db_name}")
    print(f"User: {settings.db_user}")
    
    try:
        # Try to connect to MySQL server (without specifying database first)
        print("\n1. Testing connection to MySQL server...")
        connection = mysql.connector.connect(
            host=settings.db_host,
            port=settings.db_port,
            user=settings.db_user,
            password=settings.db_password,
            connection_timeout=10
        )
        
        if connection.is_connected():
            print("✅ Successfully connected to MySQL server!")
            
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"MySQL Server version: {version[0]}")
            
            # Check if database exists
            print(f"\n2. Checking if database '{settings.db_name}' exists...")
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            db_exists = any(db[0] == settings.db_name for db in databases)
            
            if db_exists:
                print(f"✅ Database '{settings.db_name}' exists!")
                
                # Try to connect to the specific database
                print(f"\n3. Testing connection to database '{settings.db_name}'...")
                connection.close()
                
                connection = mysql.connector.connect(
                    host=settings.db_host,
                    port=settings.db_port,
                    database=settings.db_name,
                    user=settings.db_user,
                    password=settings.db_password
                )
                
                if connection.is_connected():
                    print(f"✅ Successfully connected to database '{settings.db_name}'!")
                    
                    cursor = connection.cursor()
                    cursor.execute("SHOW TABLES")
                    tables = cursor.fetchall()
                    print(f"Tables in database: {[table[0] for table in tables]}")
                    
            else:
                print(f"❌ Database '{settings.db_name}' does not exist!")
                print("Creating database...")
                cursor.execute(f"CREATE DATABASE {settings.db_name}")
                print(f"✅ Database '{settings.db_name}' created successfully!")
            
            cursor.close()
            connection.close()
            print("\n✅ All tests passed! MySQL is ready.")
            return True
            
    except Error as e:
        print(f"❌ Error connecting to MySQL: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_mysql_connection()
    if success:
        print("\n🎉 MySQL connection test successful!")
    else:
        print("\n💥 MySQL connection test failed!")
        print("\nTroubleshooting tips:")
        print("1. Make sure MySQL server is running")
        print("2. Check if the credentials are correct")
        print("3. Verify the host and port settings")
        print("4. Ensure the user has proper permissions")
