import { POPUP_STYLES } from "~/lib/utils";

interface ViewMoreContentProps {
  onClose: () => void;
  onFeaturesClick?: () => void;
  onFeedbackClick?: () => void;
  onAdminClick?: () => void;
}

export function ViewMoreContent({ 
  onClose, 
  onFeaturesClick, 
  onFeedbackClick, 
  onAdminClick 
}: ViewMoreContentProps) {
  return (
    <div className="w-full text-center relative min-h-[200px]">
      <h2 className={POPUP_STYLES.title}>
        Explore More
      </h2>
      
      <div className="flex flex-col gap-5 mt-8">
        <button 
          className={POPUP_STYLES.button}
          onClick={onFeedbackClick}
        >
          Suggestion & Feedback
        </button>
        <button 
          className={POPUP_STYLES.button}
          onClick={onFeaturesClick}
        >
          View Features
        </button>
      </div>
      
      {/* Admin Access Link */}
      <div className="absolute bottom-5 right-5">
        <button 
          className="bg-none border-none text-gray-500 text-xs cursor-pointer underline px-2 py-1 transition-colors hover:text-gray-700"
          onClick={onAdminClick}
        >
          admin
        </button>
      </div>
    </div>
  );
}

export default ViewMoreContent;
