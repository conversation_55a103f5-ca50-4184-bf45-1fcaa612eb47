import { POPUP_STYLES } from "~/lib/utils";
import { FEATURES } from "~/lib/types";

interface FeaturesContentProps {
  onClose: () => void;
}

export function FeaturesContent({ onClose }: FeaturesContentProps) {
  return (
    <div className="w-full text-left">
      <h2 className={POPUP_STYLES.title}>
        Our Features
      </h2>
      
      <div className="flex flex-col gap-6 my-8">
        {FEATURES.map((feature, index) => (
          <div 
            key={index}
            className="p-5 rounded-2xl bg-white/50 border border-law-gold/20 transition-all duration-200 hover:bg-white/80 hover:border-law-gold/40 hover:-translate-y-1"
          >
            <h3 className="bg-gold-texture bg-clip-text text-transparent font-semibold text-lg leading-tight mb-3 bg-cover bg-center">
              {feature.title}
            </h3>
            <p className="text-sm text-gray-700 leading-relaxed m-0">
              {feature.description}
            </p>
          </div>
        ))}
      </div>
      
      <button 
        className={POPUP_STYLES.button}
        onClick={onClose}
      >
        Close
      </button>
    </div>
  );
}

export default FeaturesContent;
