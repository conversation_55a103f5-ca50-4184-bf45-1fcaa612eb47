// Simple integration test to verify backend connectivity
// Run this with: node test-integration.js

import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000';

async function testHealthCheck() {
  try {
    console.log('🔍 Testing health check endpoint...');
    const response = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health check successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return false;
  }
}

async function testUserRegistration() {
  try {
    console.log('🔍 Testing user registration...');
    const userData = {
      name: 'Test User',
      email: `test.user.${Date.now()}@example.com`,
      phone_number: '+**********',
      gender: 'Male',
      profession: 'Student',
      interest_reason: 'Testing the API integration',
      user_type: 'user'
    };

    const response = await axios.post(`${API_BASE_URL}/api/users/userdata`, userData);
    console.log('✅ User registration successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ User registration failed:', error.response?.data || error.message);
    return false;
  }
}

async function testCreatorRegistration() {
  try {
    console.log('🔍 Testing creator registration...');
    const creatorData = {
      name: 'Test Creator',
      email: `test.creator.${Date.now()}@example.com`,
      phone_number: '+**********',
      gender: 'Female',
      profession: 'Lawyer',
      interest_reason: 'Testing the API integration for creators',
      user_type: 'creator'
    };

    const response = await axios.post(`${API_BASE_URL}/api/users/creatordata`, creatorData);
    console.log('✅ Creator registration successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Creator registration failed:', error.response?.data || error.message);
    return false;
  }
}

async function testNotInterestedSubmission() {
  try {
    console.log('🔍 Testing not interested submission...');
    const notInterestedData = {
      name: 'Test Not Interested',
      email: `test.notinterested.${Date.now()}@example.com`,
      phone_number: '+1234567892',
      gender: 'Other',
      profession: 'Other',
      not_interested_reason: 'Too complex',
      improvement_suggestions: 'Make it simpler',
      interest_reason: 'Just testing'
    };

    const response = await axios.post(`${API_BASE_URL}/api/notint/notinteresteddata`, notInterestedData);
    console.log('✅ Not interested submission successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Not interested submission failed:', error.response?.data || error.message);
    return false;
  }
}

async function testFeedbackSubmission() {
  try {
    console.log('🔍 Testing feedback submission...');
    const feedbackData = {
      user_email: `test.feedback.${Date.now()}@example.com`,
      visual_design_rating: 4,
      visual_design_comments: 'Good design',
      ease_of_navigation_rating: 5,
      ease_of_navigation_comments: 'Very easy to navigate',
      mobile_responsiveness_rating: 4,
      mobile_responsiveness_comments: 'Works well on mobile',
      overall_satisfaction_rating: 4,
      overall_satisfaction_comments: 'Overall satisfied',
      task_completion_rating: 5,
      task_completion_comments: 'Easy to complete tasks',
      service_quality_rating: 4,
      service_quality_comments: 'Good service quality',
      liked_features: 'Clean interface',
      improvement_suggestions: 'Add more features',
      desired_features: 'Dark mode',
      legal_challenges: 'Understanding legal terms',
      additional_feedback: 'Great platform overall',
      follow_up_consent: 'yes',
      follow_up_email: `test.followup.${Date.now()}@example.com`
    };

    const response = await axios.post(`${API_BASE_URL}/api/feedback/feedbackdata`, feedbackData);
    console.log('✅ Feedback submission successful:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Feedback submission failed:', error.response?.data || error.message);
    return false;
  }
}

async function testAdminLogin() {
  try {
    console.log('🔍 Testing admin login...');
    const adminCredentials = {
      username: 'admin',
      password: 'admin123'
    };

    const response = await axios.post(`${API_BASE_URL}/api/auth/adminlogin`, adminCredentials);
    console.log('✅ Admin login successful:', response.data);
    
    if (response.data.success && response.data.data?.access_token) {
      return response.data.data.access_token;
    }
    return null;
  } catch (error) {
    console.log('❌ Admin login failed:', error.response?.data || error.message);
    return null;
  }
}

async function testAdminEndpoints(token) {
  try {
    console.log('🔍 Testing admin endpoints...');
    const headers = {
      'Authorization': `Bearer ${token}`
    };

    // Test getting users
    const usersResponse = await axios.get(`${API_BASE_URL}/api/users/registereduserdata`, { headers });
    console.log('✅ Get users successful, count:', usersResponse.data.data?.length || 0);

    // Test getting creators
    const creatorsResponse = await axios.get(`${API_BASE_URL}/api/users/registeredcreatordata`, { headers });
    console.log('✅ Get creators successful, count:', creatorsResponse.data.data?.length || 0);

    // Test getting feedback
    const feedbackResponse = await axios.get(`${API_BASE_URL}/api/feedback/all`, { headers });
    console.log('✅ Get feedback successful, count:', feedbackResponse.data.data?.length || 0);

    return true;
  } catch (error) {
    console.log('❌ Admin endpoints failed:', error.response?.data || error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Backend Integration Tests');
  console.log('=====================================');

  const results = {
    healthCheck: false,
    userRegistration: false,
    creatorRegistration: false,
    notInterestedSubmission: false,
    feedbackSubmission: false,
    adminLogin: false,
    adminEndpoints: false
  };

  // Test health check first
  results.healthCheck = await testHealthCheck();
  
  if (!results.healthCheck) {
    console.log('\n❌ Backend is not running or not accessible at', API_BASE_URL);
    console.log('Please make sure the backend server is running on port 8000');
    return;
  }

  console.log('\n📝 Testing public endpoints...');
  results.userRegistration = await testUserRegistration();
  results.creatorRegistration = await testCreatorRegistration();
  results.notInterestedSubmission = await testNotInterestedSubmission();
  results.feedbackSubmission = await testFeedbackSubmission();

  console.log('\n🔐 Testing admin authentication...');
  const adminToken = await testAdminLogin();
  results.adminLogin = !!adminToken;

  if (adminToken) {
    console.log('\n👑 Testing admin endpoints...');
    results.adminEndpoints = await testAdminEndpoints(adminToken);
  }

  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedCount}/${totalCount} tests passed`);
  
  if (passedCount === totalCount) {
    console.log('🎉 All tests passed! Backend integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the backend server and configuration.');
  }
}

// Run the tests
runTests().catch(console.error);
