import { useState, useCallback } from "react";
import { useNavigate } from "@remix-run/react";
import Header from "./Header";
import Popup from "./ui/Popup";
import { ASSETS, Z_INDEX } from "~/lib/constants";
import type {
  UserType,
  PopupType,
  RegistrationFormData,
  NotInterestedFormData,
  FeedbackFormData
} from "~/lib/types";

export function HomePage() {
  const navigate = useNavigate();

  // Popup state
  const [showPopup, setShowPopup] = useState(false);
  const [popupType, setPopupType] = useState<PopupType>('form');
  const [userType, setUserType] = useState<UserType>('USER');
  const [isNotInterested, setIsNotInterested] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  // Form data states
  const [formData, setFormData] = useState<RegistrationFormData>({
    name: '',
    email: '',
    phone: '',
    gender: '',
    profession: '',
    whyInterested: ''
  });

  const [notInterestedData, setNotInterestedData] = useState<NotInterestedFormData>({
    name: '',
    email: '',
    whyNotInterested: ''
  });

  const [feedbackData, setFeedbackData] = useState<FeedbackFormData>({
    visualDesign: '',
    easeOfNavigation: '',
    mobileResponsiveness: '',
    overallSatisfaction: '',
    easeOfTasks: '',
    qualityOfServices: '',
    likeMost: '',
    improvements: '',
    features: '',
    legalChallenges: '',
    additionalComments: '',
    contactWilling: '',
    contactEmail: '',
    visualDesignIssue: '',
    easeOfNavigationIssue: '',
    mobileResponsivenessIssue: '',
    overallSatisfactionIssue: '',
    easeOfTasksIssue: '',
    qualityOfServicesIssue: ''
  });

  // Loading and error states
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState('');

  // Event handlers with useCallback for performance optimization
  const handleUserTypeClick = useCallback((type: UserType) => {
    setUserType(type);
    setPopupType('form');
    setShowPopup(true);
  }, []);

  const handleNotInterestedClick = useCallback(() => {
    setIsNotInterested(true);
    setPopupType('notinterested');
    setShowPopup(true);
  }, []);

  const handleFeaturesClick = useCallback(() => {
    setPopupType('features');
    setShowPopup(true);
  }, []);

  const handleFeedbackClick = useCallback(() => {
    setPopupType('feedback');
    setShowPopup(true);
  }, []);

  const handleViewMoreClick = useCallback(() => {
    setPopupType('viewmore');
    setShowPopup(true);
  }, []);

  const handleAdminClick = useCallback(() => {
    navigate('/admin');
  }, [navigate]);

  const closePopup = useCallback(() => {
    setShowPopup(false);
    setIsNotInterested(false);
    setFeedbackSubmitted(false);
    // Reset form data
    setFormData({
      name: '',
      email: '',
      phone: '',
      gender: '',
      profession: '',
      whyInterested: ''
    });
    setNotInterestedData({
      name: '',
      email: '',
      whyNotInterested: ''
    });
    setFeedbackData({
      visualDesign: '',
      easeOfNavigation: '',
      mobileResponsiveness: '',
      overallSatisfaction: '',
      easeOfTasks: '',
      qualityOfServices: '',
      likeMost: '',
      improvements: '',
      features: '',
      legalChallenges: '',
      additionalComments: '',
      contactWilling: '',
      contactEmail: '',
      visualDesignIssue: '',
      easeOfNavigationIssue: '',
      mobileResponsivenessIssue: '',
      overallSatisfactionIssue: '',
      easeOfTasksIssue: '',
      qualityOfServicesIssue: ''
    });
    setIsSubmitting(false);
    setSubmitError('');
  }, []);

  return (
    <>
      <div className="relative w-full min-h-screen overflow-hidden font-sans">
        {/* Background Video */}
        <video
          autoPlay
          muted
          loop
          className={`fixed top-0 left-0 w-full h-full object-cover z-[${Z_INDEX.BACKGROUND}]`}
          src={ASSETS.VIDEO}
        />
        
        {/* Background Overlay */}
        <div className="fixed top-0 left-0 w-full h-full bg-law-dark/30 z-0 pointer-events-none" />

        {/* Header */}
        <Header onViewMoreClick={handleViewMoreClick} />

        {/* Main Content */}
        <main className="flex w-full justify-center min-h-screen relative items-center z-10 pt-24 lg:pt-32">
          <section className="w-full max-w-fit px-4 sm:px-8 md:px-12 lg:px-16 py-0 flex flex-col justify-center items-center z-20 relative">
            {/* Title Container */}
            <div className="mb-5 text-center">
              <h1 className="text-white font-normal text-2xl sm:text-3xl md:text-4xl lg:text-5xl leading-tight m-0 w-full max-w-[600px] lg:max-w-[650px] text-center px-4">
                <span>Join the LawVriksh Beta: Be the First to Experience</span>
              </h1>
            </div>

            {/* Subtitle Container */}
            <div className="mb-6 text-center w-full">
              <p className="text-white font-normal italic text-base sm:text-lg leading-relaxed m-0 text-center w-full px-4">
                "Know your rights. Show your insights"
              </p>
            </div>

            {/* Description Container */}
            <div className="mb-10">
              <p className="text-white font-normal text-xs sm:text-sm leading-relaxed m-0 text-center w-full max-w-[620px] px-4">
                Lawvriksh isn't just a platform—it's your breakthrough. Dive
                into the law with passion and purpose, transform curiosity into
                confidence, and let your voice amplify justice. Share your work
                with pride, build a digital presence that demands attention, and
                join a movement where every insight sparks change. This is where
                learners rise, leaders shine, and your impact begins.
              </p>
            </div>

            {/* Waiting List Section */}
            <div className="flex flex-col items-center gap-6 max-w-fit w-full">
              <h2 className="bg-gold-texture bg-cover bg-center bg-clip-text text-transparent font-semibold text-xl sm:text-2xl md:text-3xl leading-tight m-0 text-center w-full px-4">
                Join Our Waiting List :
              </h2>

              <div className="flex flex-col items-center gap-5 w-full">
                {/* User Type Row */}
                <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 justify-center w-full">
                  <button
                    className="flex w-40 sm:w-48 md:w-56 lg:w-64 h-10 sm:h-11 md:h-12 px-4 sm:px-6 items-center justify-center rounded-full border-none bg-gold-texture bg-cover bg-center text-black font-medium text-sm sm:text-base md:text-lg cursor-pointer transition-all duration-200 hover:opacity-80 hover:scale-105 relative before:absolute before:inset-[-3px] before:bg-gold-texture before:bg-cover before:bg-center before:rounded-full before:z-[-1] before:brightness-75 before:contrast-125"
                    onClick={() => handleUserTypeClick('USER')}
                  >
                    USER
                  </button>
                  <button
                    className="flex w-40 sm:w-48 md:w-56 lg:w-64 h-10 sm:h-11 md:h-12 px-4 sm:px-6 items-center justify-center rounded-full border-none bg-gold-texture bg-cover bg-center text-black font-medium text-sm sm:text-base md:text-lg cursor-pointer transition-all duration-200 hover:opacity-80 hover:scale-105 relative before:absolute before:inset-[-3px] before:bg-gold-texture before:bg-cover before:bg-center before:rounded-full before:z-[-1] before:brightness-75 before:contrast-125"
                    onClick={() => handleUserTypeClick('Creator')}
                  >
                    Creator
                  </button>
                </div>

                {/* Divider */}
                <div className="w-full max-w-[250px] sm:max-w-[300px] md:max-w-[350px] h-px bg-gray-400 self-center my-2 sm:my-3" />

                {/* Not Interested Button */}
                <button
                  className="flex w-40 sm:w-48 md:w-56 lg:w-64 h-10 sm:h-11 md:h-12 px-4 sm:px-6 items-center justify-center rounded-full border-none bg-gold2-texture bg-cover bg-center text-white font-medium text-sm sm:text-base md:text-lg cursor-pointer transition-all duration-200 hover:opacity-80 hover:scale-105"
                  onClick={handleNotInterestedClick}
                >
                  Not found Interest
                </button>
              </div>
            </div>
          </section>
        </main>

        {/* Popup */}
        <Popup
          isOpen={showPopup}
          onClose={closePopup}
          type={popupType}
          userType={userType}
          isNotInterested={isNotInterested}
          feedbackSubmitted={feedbackSubmitted}
          onUserTypeClick={handleUserTypeClick}
          onNotInterestedClick={handleNotInterestedClick}
          onFeaturesClick={handleFeaturesClick}
          onFeedbackClick={handleFeedbackClick}
          onAdminClick={handleAdminClick}
        />
      </div>
    </>
  );
}

export default HomePage;
